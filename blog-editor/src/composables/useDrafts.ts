import { ref, computed } from 'vue'

export interface Draft {
  id: string
  title: string
  content: string
  excerpt: string
  tags: string[]
  publishDate: string
  createdAt: string
  updatedAt: string
  lastSaved: string
}

const DRAFTS_STORAGE_KEY = 'blog-drafts'

// Global drafts state
const drafts = ref<Draft[]>([])
const currentDraftId = ref<string | null>(null)

export function useDrafts() {
  
  // Load drafts from localStorage
  const loadDrafts = () => {
    try {
      const stored = localStorage.getItem(DRAFTS_STORAGE_KEY)
      if (stored) {
        const parsedDrafts = JSON.parse(stored)
        drafts.value = Array.isArray(parsedDrafts) ? parsedDrafts : []
      } else {
        drafts.value = []
      }
    } catch (error) {
      console.error('Error loading drafts:', error)
      drafts.value = []
    }
  }

  // Save drafts to localStorage
  const saveDrafts = () => {
    try {
      localStorage.setItem(DRAFTS_STORAGE_KEY, JSON.stringify(drafts.value))
    } catch (error) {
      console.error('Error saving drafts:', error)
    }
  }

  // Generate unique ID for draft
  const generateDraftId = () => {
    return `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Create a new draft
  const createDraft = (draftData: Omit<Draft, 'id' | 'createdAt' | 'updatedAt' | 'lastSaved'>) => {
    const now = new Date().toISOString()
    const newDraft: Draft = {
      ...draftData,
      id: generateDraftId(),
      createdAt: now,
      updatedAt: now,
      lastSaved: now
    }
    
    drafts.value.unshift(newDraft) // Add to beginning for latest first
    saveDrafts()
    currentDraftId.value = newDraft.id
    return newDraft
  }

  // Update an existing draft
  const updateDraft = (id: string, updates: Partial<Omit<Draft, 'id' | 'createdAt'>>) => {
    const index = drafts.value.findIndex(draft => draft.id === id)
    if (index !== -1) {
      const now = new Date().toISOString()
      drafts.value[index] = {
        ...drafts.value[index],
        ...updates,
        updatedAt: now,
        lastSaved: now
      }
      saveDrafts()
      return drafts.value[index]
    }
    return null
  }

  // Delete a draft
  const deleteDraft = (id: string) => {
    const index = drafts.value.findIndex(draft => draft.id === id)
    if (index !== -1) {
      drafts.value.splice(index, 1)
      saveDrafts()
      
      // If we deleted the current draft, clear current draft ID
      if (currentDraftId.value === id) {
        currentDraftId.value = null
      }
      
      return true
    }
    return false
  }

  // Get a draft by ID
  const getDraft = (id: string) => {
    return drafts.value.find(draft => draft.id === id) || null
  }

  // Get the latest draft
  const getLatestDraft = () => {
    if (drafts.value.length === 0) return null
    return drafts.value[0] // Since we add new drafts to the beginning
  }

  // Save current form data as draft
  const saveCurrentAsDraft = (formData: {
    title: string
    content: string
    excerpt: string
    tags: string[]
    publishDate: string
  }) => {
    // If we have a current draft, update it
    if (currentDraftId.value) {
      return updateDraft(currentDraftId.value, formData)
    } else {
      // Create new draft
      return createDraft(formData)
    }
  }

  // Auto-save functionality
  const autoSaveDraft = (formData: {
    title: string
    content: string
    excerpt: string
    tags: string[]
    publishDate: string
  }) => {
    // Only auto-save if there's actual content
    if (formData.title.trim() || formData.content.trim() || formData.excerpt.trim()) {
      return saveCurrentAsDraft(formData)
    }
    return null
  }

  // Clear current draft (when published or discarded)
  const clearCurrentDraft = () => {
    if (currentDraftId.value) {
      deleteDraft(currentDraftId.value)
      currentDraftId.value = null
    }
  }

  // Load a specific draft as current
  const loadDraft = (id: string) => {
    const draft = getDraft(id)
    if (draft) {
      currentDraftId.value = id
      return draft
    }
    return null
  }

  // Computed properties
  const sortedDrafts = computed(() => {
    return [...drafts.value].sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    )
  })

  const currentDraft = computed(() => {
    return currentDraftId.value ? getDraft(currentDraftId.value) : null
  })

  const hasDrafts = computed(() => drafts.value.length > 0)

  const draftsCount = computed(() => drafts.value.length)

  // Initialize on first use
  loadDrafts()

  return {
    // State
    drafts: sortedDrafts,
    currentDraft,
    currentDraftId,
    hasDrafts,
    draftsCount,
    
    // Methods
    createDraft,
    updateDraft,
    deleteDraft,
    getDraft,
    getLatestDraft,
    saveCurrentAsDraft,
    autoSaveDraft,
    clearCurrentDraft,
    loadDraft,
    loadDrafts,
    saveDrafts
  }
}
