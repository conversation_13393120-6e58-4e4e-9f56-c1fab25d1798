import { useProfileStore } from '@/stores/profileStore'

export function useAuth() {
  const profileStore = useProfileStore()

  // Return store properties and methods
  return {
    // State
    user: profileStore.user,
    profile: profileStore.profile,
    loading: profileStore.loading,
    error: profileStore.error,

    // Computed
    isAuthenticated: profileStore.isAuthenticated,
    isAdmin: profileStore.isAdmin,
    isEditor: profileStore.isEditor,
    canWrite: profileStore.canWrite,

    // Methods
    initialize: profileStore.initialize,
    setupAuthListener: profileStore.setupAuthListener,
    logout: profileStore.logout,
    refreshProfile: profileStore.refreshProfile,
    hasRole: profileStore.hasRole,
    hasAnyRole: profileStore.hasAnyRole
  }
}

// Auto-initialize on first import
let initialized = false

export async function initializeAuth() {
  if (initialized) return

  const profileStore = useProfileStore()
  await profileStore.initialize()
  profileStore.setupAuthListener()

  initialized = true
}
