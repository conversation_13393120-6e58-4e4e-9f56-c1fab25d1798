import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, getCurrentUser, getCurrentProfile, signOut, type Profile } from '@/lib/supabase'
import type { User } from '@supabase/supabase-js'

export const useProfileStore = defineStore('profile', () => {
  // State
  const user = ref<User | null>(null)
  const profile = ref<Profile | null>(null)
  const loading = ref(true)
  const error = ref<string | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => profile.value?.role === 'admin')
  const isEditor = computed(() => ['admin', 'editor'].includes(profile.value?.role || ''))
  const canWrite = computed(() => ['admin', 'editor', 'writer'].includes(profile.value?.role || ''))

  // Actions
  const initialize = async () => {
    try {
      loading.value = true
      error.value = null

      // Get current session
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        user.value = session.user
        
        // Load user profile
        try {
          profile.value = await getCurrentProfile()
        } catch (profileError) {
          console.error('Error loading profile:', profileError)
          // Don't set error here as user is still authenticated
        }
      } else {
        user.value = null
        profile.value = null
      }
    } catch (err) {
      console.error('Auth initialization error:', err)
      error.value = 'Failed to initialize authentication'
      user.value = null
      profile.value = null
    } finally {
      loading.value = false
    }
  }

  // Setup auth state listener
  const setupAuthListener = () => {
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email)

      if (event === 'SIGNED_IN' && session?.user) {
        user.value = session.user

        // Load user profile
        try {
          profile.value = await getCurrentProfile()
        } catch (profileError) {
          console.error('Error loading profile after sign in:', profileError)
        }

      } else if (event === 'SIGNED_OUT') {
        user.value = null
        profile.value = null
      } else if (event === 'TOKEN_REFRESHED' && session?.user) {
        // Update user data on token refresh but don't change loading state
        user.value = session.user
        console.log('Token refreshed successfully')
      }
    })
  }

  // Setup profile subscription for real-time updates
  const setupProfileSubscription = () => {
    if (!user.value) return

    const subscription = supabase
      .channel('profile-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'blog',
          table: 'profiles',
          filter: `id=eq.${user.value.id}`
        },
        (payload) => {
          console.log('Profile updated:', payload)
          if (payload.eventType === 'UPDATE' && payload.new) {
            profile.value = payload.new as Profile
          }
        }
      )
      .subscribe()

    return subscription
  }

  // Sign out
  const logout = async () => {
    try {
      loading.value = true
      await signOut()
      user.value = null
      profile.value = null
    } catch (err) {
      console.error('Logout error:', err)
      error.value = 'Failed to sign out'
    } finally {
      loading.value = false
    }
  }

  // Refresh profile data
  const refreshProfile = async () => {
    if (!user.value) return
    
    try {
      profile.value = await getCurrentProfile()
    } catch (err) {
      console.error('Error refreshing profile:', err)
    }
  }

  // Check if user has specific role
  const hasRole = (role: string) => {
    return profile.value?.role === role
  }

  // Check if user has any of the specified roles
  const hasAnyRole = (roles: string[]) => {
    return roles.includes(profile.value?.role || '')
  }

  return {
    // State
    user,
    profile,
    loading,
    error,
    
    // Computed
    isAuthenticated,
    isAdmin,
    isEditor,
    canWrite,
    
    // Actions
    initialize,
    setupAuthListener,
    setupProfileSubscription,
    logout,
    refreshProfile,
    hasRole,
    hasAnyRole
  }
})
