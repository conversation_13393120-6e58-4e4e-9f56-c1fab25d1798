<script setup lang="ts">
import { computed } from 'vue'
import type { SidebarProps } from '@/components/ui/sidebar'
import { useRouter } from 'vue-router'

import {
  BarChart3,
  FileText,
  User,
  Settings,
} from "lucide-vue-next"
import NavMain from '@/components/NavMain.vue'
import NavUser from '@/components/NavUser.vue'
import { useAuth } from '@/composables/useAuth'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar'

const router = useRouter()
const { user, profile } = useAuth()
const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: "icon",
})

// Custom navigation data matching original structure
const data = computed(() => ({
  user: {
    name: profile.value?.full_name || profile.value?.username || user.value?.email || "User",
    email: user.value?.email || "No email",
    avatar: profile.value?.avatar_url || "",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: BarChart3,
      isActive: false,
    },
    {
      title: "Articles",
      url: "/articles",
      icon: FileText,
      isActive: false,
      items: [
        {
          title: "All articles",
          url: "/articles",
        },
        {
          title: "Write a new article",
          url: "/articles/new",
        },
      ],
    },
    {
      title: "Profile",
      url: "/profile",
      icon: User,
      isActive: false,
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
      isActive: false,
    },
  ],
}))

// Handle navigation for non-collapsible items
const handleNavigation = (url: string) => {
  router.push(url)
}
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <div class="flex items-center gap-2 px-4 py-2">
        <div class="bg-primary flex items-center justify-center w-8 h-8 rounded-lg">
          <span class="text-primary-foreground font-bold">V</span>
        </div>
        <span class="text-lg font-bold">BlogEditor</span>
      </div>
    </SidebarHeader>
    <SidebarContent>
      <NavMain
        :items="data.navMain"
        :onNavigate="handleNavigation"
        :collapsibleOnly="true"
      />
    </SidebarContent>
    <SidebarFooter>
      <NavUser :user="data.user" />
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>
