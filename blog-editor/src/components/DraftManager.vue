<template>
  <div class="space-y-4">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">Drafts</h3>
        <p class="text-sm text-muted-foreground">
          {{ draftsCount }} draft{{ draftsCount !== 1 ? 's' : '' }} saved
        </p>
      </div>
      <Button 
        v-if="hasDrafts" 
        variant="outline" 
        size="sm"
        @click="showDeleteAllDialog = true"
      >
        Clear All
      </Button>
    </div>

    <!-- No drafts state -->
    <div v-if="!hasDrafts" class="text-center py-8">
      <div class="text-muted-foreground">
        <svg class="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="text-sm">No drafts saved yet</p>
        <p class="text-xs">Start writing to automatically save drafts</p>
      </div>
    </div>

    <!-- Drafts list -->
    <div v-else class="space-y-2 max-h-96 overflow-y-auto">
      <div
        v-for="draft in drafts"
        :key="draft.id"
        class="border rounded-lg p-3 hover:bg-muted/50 transition-colors"
        :class="{ 'ring-2 ring-primary': currentDraftId === draft.id }"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1 min-w-0">
            <h4 class="font-medium text-sm truncate">
              {{ draft.title || 'Untitled Draft' }}
            </h4>
            <p class="text-xs text-muted-foreground mt-1 line-clamp-2">
              {{ draft.excerpt || draft.content.substring(0, 100) || 'No content' }}
            </p>
            <div class="flex items-center gap-2 mt-2">
              <span class="text-xs text-muted-foreground">
                {{ formatDate(draft.updatedAt) }}
              </span>
              <div v-if="draft.tags.length > 0" class="flex gap-1">
                <Badge 
                  v-for="tag in draft.tags.slice(0, 2)" 
                  :key="tag" 
                  variant="secondary" 
                  class="text-xs px-1 py-0"
                >
                  {{ tag }}
                </Badge>
                <span v-if="draft.tags.length > 2" class="text-xs text-muted-foreground">
                  +{{ draft.tags.length - 2 }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="flex items-center gap-1 ml-2">
            <Button
              variant="ghost"
              size="sm"
              @click="loadDraftHandler(draft.id)"
              :disabled="currentDraftId === draft.id"
            >
              {{ currentDraftId === draft.id ? 'Current' : 'Load' }}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click="deleteDraftHandler(draft.id)"
            >
              <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete all confirmation dialog -->
    <div v-if="showDeleteAllDialog" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-background border rounded-lg p-6 max-w-sm mx-4">
        <h3 class="font-semibold mb-2">Delete All Drafts</h3>
        <p class="text-sm text-muted-foreground mb-4">
          Are you sure you want to delete all {{ draftsCount }} drafts? This action cannot be undone.
        </p>
        <div class="flex gap-2 justify-end">
          <Button variant="outline" size="sm" @click="showDeleteAllDialog = false">
            Cancel
          </Button>
          <Button variant="destructive" size="sm" @click="deleteAllDrafts">
            Delete All
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useDrafts } from '@/composables/useDrafts'
import { toast } from 'vue-sonner'

const emit = defineEmits<{
  draftLoaded: [draft: any]
}>()

const { 
  drafts, 
  currentDraftId, 
  hasDrafts, 
  draftsCount, 
  deleteDraft, 
  loadDraft 
} = useDrafts()

const showDeleteAllDialog = ref(false)

// Format date for display
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`
  
  return date.toLocaleDateString()
}

// Load a draft
const loadDraftHandler = (id: string) => {
  const draft = loadDraft(id)
  if (draft) {
    emit('draftLoaded', draft)
    toast.success('Draft loaded successfully')
  }
}

// Delete a single draft
const deleteDraftHandler = (id: string) => {
  if (deleteDraft(id)) {
    toast.success('Draft deleted')
  }
}

// Delete all drafts
const deleteAllDrafts = () => {
  // Clear localStorage
  localStorage.removeItem('blog-drafts')
  // Reload drafts to update state
  drafts.value.splice(0)
  showDeleteAllDialog.value = false
  toast.success('All drafts deleted')
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
