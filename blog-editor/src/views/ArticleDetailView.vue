<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'

// Dummy article data
const article = ref({
  id: '1',
  title: 'Getting Started with Vue 3',
  content: 'This is a sample article about Vue 3. It covers the basics of the framework and how to get started with building modern web applications.',
  excerpt: 'Learn the fundamentals of Vue 3 and start building modern web applications.',
  author: '<PERSON>',
  date: '2024-01-15',
  readTime: '5 min read',
  views: 1234,
  likes: 42,
  tags: ['Vue.js', 'JavaScript', 'Frontend'],
  category: 'Tutorial'
})

// Format date for display
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">{{ article.title }}</h1>
        <p class="text-muted-foreground">{{ article.excerpt }}</p>
      </div>
      <div class="flex gap-2">
        <Button variant="outline">Edit</Button>
        <Button variant="outline">Delete</Button>
      </div>
    </div>

    <!-- Article Meta -->
    <Card>
      <CardContent class="pt-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <Avatar>
              <AvatarFallback>{{ article.author.split(' ').map(n => n[0]).join('') }}</AvatarFallback>
            </Avatar>
            <div>
              <p class="font-medium">{{ article.author }}</p>
              <p class="text-muted-foreground text-sm">{{ formatDate(article.date) }}</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-muted-foreground text-sm">{{ article.readTime }}</p>
            <p class="text-muted-foreground text-sm">{{ article.views }} views</p>
          </div>
        </div>
        
        <Separator class="my-4" />
        
        <div class="flex flex-wrap gap-2">
          <Badge v-for="tag in article.tags" :key="tag" variant="secondary">
            {{ tag }}
          </Badge>
        </div>
      </CardContent>
    </Card>

    <!-- Article Content -->
    <Card>
      <CardContent class="pt-6">
        <div 
          class="max-w-none prose prose-lg"
          :class="{ 'prose-invert': document.documentElement.classList.contains('dark') }"
          v-html="article.content.replace(/\n/g, '<br>')"
        ></div>
      </CardContent>
    </Card>

    <!-- Article Actions -->
    <Card>
      <CardContent class="pt-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <Button variant="outline" class="flex items-center gap-2">
              <span>👍</span> Like ({{ article.likes }})
            </Button>
            <Button variant="outline" class="flex items-center gap-2">
              <span>💬</span> Comment
            </Button>
            <Button variant="outline" class="flex items-center gap-2">
              <span>🔗</span> Share
            </Button>
          </div>
          <div class="flex items-center gap-2">
            <Button variant="outline">
              <span>📧</span> Email
            </Button>
            <Button variant="outline">
              <span>📋</span> Copy Link
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<style scoped>
.prose {
  line-height: 1.7;
}

.prose h1 {
  font-size: 2.5em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.prose h2 {
  font-size: 2em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.prose h3 {
  font-size: 1.5em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.prose p {
  margin-bottom: 1em;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-family: 'Fira Code', monospace;
}

.prose pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
}

.prose blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
}

.prose ul, .prose ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}

.prose li {
  margin-bottom: 0.5em;
}

.prose-inverse h1,
.prose-inverse h2,
.prose-inverse h3,
.prose-inverse p,
.prose-inverse li {
  color: #e5e7eb;
}

.prose-inverse code {
  background-color: #374151;
  color: #f9fafb;
}

.prose-inverse pre {
  background-color: #111827;
}
</style>