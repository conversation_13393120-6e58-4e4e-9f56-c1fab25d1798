<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { useDashboardStore } from '@/stores/dashboardStore'
import { useProfileStore } from '@/stores/profileStore'

const router = useRouter()
const dashboardStore = useDashboardStore()
const profileStore = useProfileStore()

// Use store computed properties
const dashboardStats = computed(() => dashboardStore.stats)
const recentActivity = computed(() =>
  dashboardStore.recentArticles.map(article => ({
    id: article.id,
    title: article.title,
    status: article.status,
    updated_at: article.updated_at,
    timeAgo: getTimeAgo(article.updated_at)
  }))
)

// Helper function to get time ago
const getTimeAgo = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`
  return date.toLocaleDateString()
}

// Load dashboard data using store
const loadDashboardData = async () => {
  await dashboardStore.initialize()
}

// Navigation functions
const goToNewArticle = () => {
  router.push('/articles/new')
}

const goToArticle = (articleId: string) => {
  router.push(`/articles/${articleId}`)
}

const goToArticles = () => {
  router.push('/articles')
}

// Load data on mount
onMounted(() => {
  loadDashboardData()
})
</script>

<template>
  <div v-if="dashboardStore.loading" class="p-6 flex items-center justify-center min-h-[400px]">
    <div class="text-center space-y-4">
      <div class="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
      <p class="text-muted-foreground">Loading dashboard...</p>
    </div>
  </div>

  <div v-else-if="dashboardStore.error" class="p-6 flex items-center justify-center min-h-[400px]">
    <div class="text-center space-y-4">
      <p class="text-red-500">{{ dashboardStore.error }}</p>
      <Button @click="loadDashboardData">Try Again</Button>
    </div>
  </div>

  <div v-else class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">Dashboard</h1>
        <p class="text-muted-foreground">
          Welcome back, {{ profileStore.profile?.full_name || profileStore.profile?.username || 'Writer' }}!
        </p>
      </div>
      <div class="flex items-center gap-4">
        <Button @click="goToNewArticle">
          New Article
        </Button>
        <Avatar>
          <AvatarImage :src="profileStore.profile?.avatar_url" />
          <AvatarFallback>
            {{ profileStore.profile?.full_name?.charAt(0) || profileStore.profile?.username?.charAt(0) || 'U' }}
          </AvatarFallback>
        </Avatar>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="md:grid-cols-2 lg:grid-cols-4 grid grid-cols-1 gap-6">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle class="text-sm font-medium">Total Articles</CardTitle>
          <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ dashboardStats.totalArticles }}</div>
          <p class="text-muted-foreground text-xs">
            +12% from last month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle class="text-sm font-medium">Published</CardTitle>
          <div class="w-4 h-4 bg-green-500 rounded-full"></div>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ dashboardStats.publishedArticles }}</div>
          <p class="text-muted-foreground text-xs">
            {{ dashboardStats.archivedArticles }} archived
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle class="text-sm font-medium">Total Words</CardTitle>
          <div class="w-4 h-4 bg-yellow-500 rounded-full"></div>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ dashboardStats.totalWords.toLocaleString() }}</div>
          <p class="text-muted-foreground text-xs">
            across all articles
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between pb-2 space-y-0">
          <CardTitle class="text-sm font-medium">Draft Articles</CardTitle>
          <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ dashboardStats.draftArticles }}</div>
          <p class="text-muted-foreground text-xs">
            ready to publish
          </p>
        </CardContent>
      </Card>
    </div>

    <!-- Quick Actions -->
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-4">
          <Button @click="goToNewArticle">
            <span class="mr-2">+</span> New Article
          </Button>
          <Button variant="outline" @click="goToArticles">
            View All Articles
          </Button>
          <Button variant="outline">
            Settings
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Recent Activity -->
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div v-if="recentActivity.length === 0" class="text-center py-8 text-muted-foreground">
            <p>No articles yet. Create your first article to get started!</p>
            <Button @click="goToNewArticle" class="mt-4">
              Create Article
            </Button>
          </div>

          <div v-else v-for="activity in recentActivity" :key="activity.id" class="flex items-center space-x-4 cursor-pointer hover:bg-muted/50 p-2 rounded-lg transition-colors" @click="goToArticle(activity.id)">
            <div class="flex-shrink-0">
              <div :class="{
                'w-2 h-2 bg-green-500 rounded-full': activity.status === 'published',
                'w-2 h-2 bg-yellow-500 rounded-full': activity.status === 'draft',
                'w-2 h-2 bg-gray-500 rounded-full': activity.status === 'archived'
              }"></div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-foreground text-sm font-medium line-clamp-1">
                {{ activity.title }}
              </p>
              <p class="text-muted-foreground text-xs">
                {{ activity.timeAgo }}
              </p>
            </div>
            <Badge :variant="activity.status === 'published' ? 'default' : 'secondary'" class="text-xs">
              {{ activity.status }}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>