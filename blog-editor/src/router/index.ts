import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuth } from '@/composables/useAuth'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginView.vue'),
    meta: { title: 'Login' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/DashboardView.vue'),
    meta: { requiresAuth: true, title: 'Dashboard' }
  },
  {
    path: '/articles',
    name: 'Articles',
    component: () => import('@/views/ArticlesView.vue'),
    meta: { requiresAuth: true, title: 'Articles' }
  },
  {
    path: '/articles/:id',
    name: 'ArticleDetail',
    component: () => import('@/views/ArticleDetailView.vue'),
    meta: { requiresAuth: true, title: 'Article' }
  },
  {
    path: '/articles/new',
    name: 'NewArticle',
    component: () => import('@/views/NewArticleView.vue'),
    meta: { requiresAuth: true, title: 'New Article' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/ProfileView.vue'),
    meta: { requiresAuth: true, title: 'Profile' }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/SettingsView.vue'),
    meta: { requiresAuth: true, title: 'Settings' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  try {
    // Use cached auth state instead of making API calls on every navigation
    const auth = useAuth()

    // If auth is still loading, wait a bit for it to complete
    if (auth.loading) {
      // Wait up to 2 seconds for auth to complete
      let attempts = 0
      while (auth.loading && attempts < 20) {
        await new Promise(resolve => setTimeout(resolve, 100))
        attempts++
      }
    }

    const isAuthenticated = auth.isAuthenticated

    if (to.meta.requiresAuth && !isAuthenticated) {
      // Store the intended destination for redirect after login
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
    } else if (to.path === '/login' && isAuthenticated) {
      next('/dashboard')
    } else {
      next()
    }
  } catch (error) {
    console.error('Auth check error:', error)
    // If there's an error checking auth, redirect to login for protected routes
    if (to.meta.requiresAuth) {
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
    } else {
      next()
    }
  }
})

export default router