import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'http://***************:8000'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6ImFidWgxMjMxIiwiaWF0IjoxNzQ6Mzg1MjAwLCJleHAiOjE5MDQxNTE2MDB9.EkgKcDa0V0VEmPsXPmyal3YvxYuu9Q1k8OZZv7Gs8_o'

// Get the correct redirect URL based on environment
const getRedirectUrl = () => {
  const isDev = import.meta.env.DEV
  if (isDev) {
    return 'http://localhost:5173'
  }
  return 'https://blog.voicehype.ai'
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true
  },
  db: {
    schema: 'blog'
  }
})

// Database types
export interface Profile {
  id: string
  username: string | null
  full_name: string | null
  email: string | null
  avatar_url: string | null
  website: string | null
  role: 'admin' | 'editor' | 'writer' | 'author' | 'reader'
  bio: string | null
  created_at: string
  updated_at: string
}

export interface Article {
  id: string
  title: string
  slug: string
  content: string | null
  excerpt: string | null
  cover_image_url: string | null
  author_id: string
  status: 'draft' | 'published' | 'archived'
  published_at: string | null
  created_at: string
  updated_at: string
  meta_title: string | null
  meta_description: string | null
  featured: boolean
}

export interface Tag {
  id: string
  name: string
  slug: string
  description: string | null
  created_at: string
  updated_at: string
}

export interface ArticleTag {
  article_id: string
  tag_id: string
}

// Authentication helpers
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

export const getCurrentProfile = async () => {
  const user = await getCurrentUser()
  if (!user) return null

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (error) throw error
  return profile as Profile
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  if (error) throw error
  return data
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

// OAuth authentication
export const signInWithOAuth = async (provider: 'google' | 'github') => {
  const redirectUrl = getRedirectUrl()

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: redirectUrl
    }
  })

  if (error) throw error
  return data
}

export const signInWithGoogle = async () => {
  return signInWithOAuth('google')
}

export const signInWithGitHub = async () => {
  return signInWithOAuth('github')
}

// Handle OAuth callback
export const handleOAuthCallback = async () => {
  const { data, error } = await supabase.auth.getSession()
  if (error) throw error
  return data
}

// Article operations
// Helper function to create or get tag by name
const createOrGetTag = async (tagName: string): Promise<string> => {
  const slug = tagName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')

  // First try to get existing tag
  const { data: existingTag } = await supabase
    .from('tags')
    .select('id')
    .eq('name', tagName)
    .single()

  if (existingTag) {
    return existingTag.id
  }

  // Create new tag if it doesn't exist
  const { data: newTag, error } = await supabase
    .from('tags')
    .insert([{ name: tagName, slug }])
    .select('id')
    .single()

  if (error) {
    throw new Error(`Failed to create tag: ${error.message}`)
  }

  return newTag.id
}



export const createArticle = async (
  articleData: Partial<Article>,
  tags: string[] = []
) => {
  const user = await getCurrentUser()
  if (!user) throw new Error('User not authenticated')

  const { data: article, error: articleError } = await supabase
    .from('articles')
    .insert({
      ...articleData,
      author_id: user.id
    })
    .select()
    .single()

  if (articleError) throw articleError

  // Create/get tags and link them to the article
  if (tags.length > 0) {
    try {
      const tagIds = await Promise.all(tags.map(createOrGetTag))

      const articleTags = tagIds.map(tagId => ({
        article_id: article.id,
        tag_id: tagId
      }))

      const { error: tagError } = await supabase
        .from('article_tags')
        .insert(articleTags)

      if (tagError) {
        console.error('Error linking tags:', tagError)
      }
    } catch (error) {
      console.error('Error processing tags:', error)
    }
  }



  return article as Article
}

export const updateArticle = async (id: string, articleData: Partial<Article>) => {
  const { data, error } = await supabase
    .from('articles')
    .update({
      ...articleData,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return data as Article
}

export const getArticle = async (id: string) => {
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      profiles:author_id (
        id,
        username,
        full_name,
        avatar_url
      )
    `)
    .eq('id', id)
    .single()

  if (error) throw error
  return data
}

export const getArticleBySlug = async (slug: string) => {
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      profiles:author_id (
        id,
        username,
        full_name,
        avatar_url
      )
    `)
    .eq('slug', slug)
    .single()

  if (error) throw error
  return data
}

export const getUserArticles = async (userId?: string) => {
  const user = userId || (await getCurrentUser())?.id
  if (!user) throw new Error('User not authenticated')

  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      profiles:author_id (
        id,
        username,
        full_name,
        avatar_url
      )
    `)
    .eq('author_id', user)
    .order('updated_at', { ascending: false })

  if (error) throw error
  return data
}

export const deleteArticle = async (id: string) => {
  const { error } = await supabase
    .from('articles')
    .delete()
    .eq('id', id)

  if (error) throw error
}

// Slug validation
export const checkSlugExists = async (slug: string, excludeId?: string) => {
  let query = supabase
    .from('articles')
    .select('id')
    .eq('slug', slug)

  if (excludeId) {
    query = query.neq('id', excludeId)
  }

  const { data, error } = await query

  if (error) throw error
  return data && data.length > 0
}

// Tag operations
export const getTags = async () => {
  const { data, error } = await supabase
    .from('tags')
    .select('*')
    .order('name')

  if (error) throw error
  return data as Tag[]
}

export const createTag = async (tagData: Partial<Tag>) => {
  const { data, error } = await supabase
    .from('tags')
    .insert(tagData)
    .select()
    .single()

  if (error) throw error
  return data as Tag
}



// Article tags/categories management
export const setArticleTags = async (articleId: string, tagIds: string[]) => {
  // First, remove existing tags
  await supabase
    .from('article_tags')
    .delete()
    .eq('article_id', articleId)

  // Then add new tags
  if (tagIds.length > 0) {
    const { error } = await supabase
      .from('article_tags')
      .insert(
        tagIds.map(tagId => ({
          article_id: articleId,
          tag_id: tagId
        }))
      )

    if (error) throw error
  }
}


