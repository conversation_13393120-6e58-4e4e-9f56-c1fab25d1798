import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { initializeAuth } from '@/composables/useAuth'

// Import Prism.js CSS for syntax highlighting
import 'prismjs/themes/prism-tomorrow.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// Initialize authentication before mounting
initializeAuth().then(() => {
  app.mount('#app')
})
