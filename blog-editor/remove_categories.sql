-- SQL script to remove categories system from blog schema
-- Run this on your Supabase database to remove categories

-- First, drop the policies for categories and article_categories tables
DROP POLICY IF EXISTS "Article categories are viewable by everyone" ON blog.article_categories;
DROP POLICY IF EXISTS "Authors can manage categories for their articles" ON blog.article_categories;
DROP POLICY IF EXISTS "Admins and editors can manage categories" ON blog.categories;
DROP POLICY IF EXISTS "Categories are viewable by everyone" ON blog.categories;

-- Drop the indexes
DROP INDEX IF EXISTS blog.idx_article_categories_article_id;
DROP INDEX IF EXISTS blog.idx_article_categories_category_id;

-- Drop the junction table first (due to foreign key constraints)
DROP TABLE IF EXISTS blog.article_categories;

-- Drop the categories table
DROP TABLE IF EXISTS blog.categories;

-- Note: This will permanently remove all category data
-- Make sure to backup your data before running this script
