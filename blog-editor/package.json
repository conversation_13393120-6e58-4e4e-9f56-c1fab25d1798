{"name": "blog-editor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@supabase/supabase-js": "^2.56.0", "@types/prismjs": "^1.26.5", "@vueuse/core": "^13.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.539.0", "pinia": "^3.0.3", "prismjs": "^1.30.0", "reka-ui": "^2.4.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-sonner": "^2.0.8"}, "devDependencies": {"@tailwindcss/vite": "^4.1.12", "@types/node": "^24.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "tailwindcss": "^4.1.12", "typescript": "~5.8.3", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}